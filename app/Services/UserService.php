<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserService
{
    public function getAllUsers($perPage = 10)
    {
        return User::with('roles')->paginate($perPage);
    }

    public function getUserById($id)
    {
        return User::with('roles')->findOrFail($id);
    }

    public function createUser(array $data)
    {
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }
        $user = User::create($data);
        
        if (isset($data['role'])) {
            $user->assignRole($data['role']);
        }
        
        return $user;
    }

    public function updateUser($id, array $data)
    {
        $user = User::findOrFail($id);
        
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }
        
        $user->update($data);
        
        if (isset($data['role'])) {
            $user->syncRoles([$data['role']]);
        }
        
        return $user;
    }

    public function deleteUser($id)
    {
        $user = User::findOrFail($id);
        return $user->delete();
    }

    public function getLatestUsers($limit = 5)
    {
        return User::latest()->limit($limit)->get();
    }

    public function getTotalUsersCount()
    {
        return User::count();
    }
}