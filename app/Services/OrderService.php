<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderDriver;
use App\Models\PointTransaction;
use App\Models\Setting;
use App\Models\User;

class OrderService
{
    /**
     * Create a new order with a unique barcode.
     *
     * @return Order
     *
     * @throws \Exception
     */
    public function create(array $data)
    {
        // Generate a unique barcode for the order
        $data['barcode'] = $this->generateUniqueBarcode();
        $data['user_id'] = auth()->id();

        // Create the order
        $order = Order::create($data);

        // Add initial tracking for the new order
        $order->trackings()->create([
            'status' => 'pending',
            'note' => 'Order created',
            'user_id' => auth()->id(),
        ]);

        return $order->load(['fromStation', 'toStation', 'type']);
    }

    public function update(Order $order, array $data)
    {
        $order->update($data);

        return $order;
    }

    public function delete(Order $order)
    {
        return $order->delete();
    }

    public function restore(Order $order)
    {
        return $order->restore();
    }

    public function forceDelete(Order $order)
    {
        return $order->forceDelete();
    }

    public function getAll($perPage = 10, $withTrashed = false)
    {
        $query = Order::with(['fromStation', 'toStation', 'type']);

        if ($withTrashed) {
            $query->onlyTrashed();
        }

        return $query->paginate($perPage);
    }

    public function findById($id)
    {
        return Order::with(['fromStation', 'toStation', 'type', 'latestTracking.user'])->findOrFail($id);
    }

    public function getByStatus($status, $perPage = 10)
    {
        return Order::with(['fromStation', 'toStation', 'type'])
            ->where('status', $status)
            ->paginate($perPage);
    }

    public function updateStatus(Order $order, $status, $note = null)
    {
        $order->update(['status' => $status]);

        $order->trackings()->create([
            'status' => $status,
            'note' => $note ?? 'Status updated',
            'user_id' => auth()->id(),
        ]);

        if ($status === 'delivered' && $order->orderDriver) {
            $order->orderDriver->update(['completed_at' => now()]);
            
            // منح النقاط للسائق عند تسليم الطلب
            try {
                $this->givePoint(
                    userId: $order->orderDriver->driver_id,
                    type: 'order_completed',
                    amount: $order->price,
                    description: 'Bonus points for order delivery',
                    orderId: $order->id
                );
            } catch (\Exception $e) {
                // تسجيل الخطأ في السجلات ولكن لا نوقف العملية
                \Log::error('فشل في منح النقاط للسائق: '.$e->getMessage());
            }
        }

        return $order;
    }

    public function getCreatedOrders($userId, $filters = [], $perPage = 10)
    {
        $query = Order::with(['fromStation', 'toStation', 'type', 'latestTracking.user'])
            ->where('user_id', $userId);

        if (!empty($filters['from_station_id'])) {
            $query->where('from_station_id', $filters['from_station_id']);
        }

        if (!empty($filters['to_station_id'])) {
            $query->where('to_station_id', $filters['to_station_id']);
        }

        if (!empty($filters['type_id'])) {
            $query->where('type_id', $filters['type_id']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    public function getReceivedOrders($userId, $filters = [], $perPage = 10)
    {
        // الطلبات التي قام بتوصيلها (مع معرف طلب)
        $orders = Order::with(['fromStation', 'toStation', 'type', 'latestTracking.user', 'orderDriver.driver'])->where('status', 'picked_up')
            ->whereHas('orderDriver', function ($q) use ($userId) {
                $q->where('driver_id', $userId)->whereNotNull('order_id');
            })
            ->orderBy('created_at', 'desc')
            ->get();

        // الاهتمامات بدون طلبات (بدون معرف طلب)
        $interests = OrderDriver::with(['type', 'fromStation', 'toStation'])
            ->where('driver_id', $userId)
            ->whereNull('order_id')
            ->orderBy('created_at', 'desc')
            ->get();

        // دمج النتائج
        $combined = collect();

        // إضافة الطلبات
        foreach ($orders as $order) {
            $combined->push($order);
        }

        // إضافة الاهتمامات
        foreach ($interests as $interest) {
            $combined->push($interest);
        }

        return $combined->sortByDesc('created_at');
    }

    public function searchAvailableDeliveries($userId, $fromStationId, $toStationId)
    {
        $availableOrders = Order::with('type')
            ->where('user_id', '!=', $userId)
            ->where('status', 'confirmed')
            ->where('from_station_id', $fromStationId)
            ->where('to_station_id', $toStationId)
            ->whereDoesntHave('orderDriver')
            ->get()
            ->groupBy('type.id');

        $result = [];
        foreach ($availableOrders as $typeId => $orders) {
            // حساب عدد الأشخاص المهتمين
            $interestedCount = \App\Models\Attendance::where('type_id', $typeId)
                ->where('from_station_id', $fromStationId)
                ->where('to_station_id', $toStationId)
                ->count();

            // حساب النسبة المئوية
            $ordersCount = $orders->count();
            $deliveryChance = $ordersCount > $interestedCount 
                ? 100 : ($interestedCount > 0 ? round(($ordersCount / $interestedCount) * 100, 1) : 100);

            $result[] = [
                'type_name' => $orders->first()->type->name,
                'shipments_count' => $orders->count(),
                'interested_people' => $interestedCount,
                'delivery_chance' => $deliveryChance.'%',
            ];
        }

        return $result;
    }

    public function registerInterest($driverId, $typeId, $fromStationId, $toStationId)
    {
        // تسجيل اهتمام السائق بالنوع والمحطات
        return OrderDriver::create([
            'driver_id' => $driverId,
            'type_id' => $typeId,
            'from_station_id' => $fromStationId,
            'to_station_id' => $toStationId,
            'assigned_at' => now(),
        ]);
    }

    public function assignRandomOrder($interestId)
    {
        $interest = OrderDriver::findOrFail($interestId);

        // اختيار طلب عشوائي من الطلبات المتاحة
        $order = Order::where('user_id', '!=', $interest->driver_id)
            ->where('type_id', $interest->type_id)
            ->where('from_station_id', $interest->from_station_id)
            ->where('to_station_id', $interest->to_station_id)
            ->where('status', 'confirmed')
            ->whereDoesntHave('orderDriver')
            ->inRandomOrder()
            ->first();

        if (!$order) {
            throw new \Exception(__('no_available_orders'));
        }

        // ربط الاهتمام بالطلب
        $interest->update(['order_id' => $order->id]);

        // تحديث حالة الطلب والتراكنج
        $this->updateStatus($order, 'picked_up', 'Order picked up by driver');

        return $order->fresh(['fromStation', 'toStation', 'type', 'latestTracking.user', 'orderDriver.driver']);
    }

    /**
     * Get user order history with optional status filtering
     *
     * @param int $userId
     * @param string|null $status Filter by order status (e.g., 'pending', 'confirmed', 'picked_up', 'delivered', 'cancelled')
     * @return array
     */
    public function getUserHistory($userId, ?string $status = null)
    {
        // Base query for created orders
        $createdQuery = Order::with(['fromStation', 'toStation', 'type', 'latestTracking.user'])
            ->where('user_id', $userId);

        // Apply status filter if provided
        if ($status) {
            $createdQuery->where('status', $status);
        }

        $createdOrders = $createdQuery->orderBy('created_at', 'desc')->get();

        // Base query for delivered orders
        $deliveredQuery = Order::with(['fromStation', 'toStation', 'type', 'latestTracking.user', 'orderDriver.driver'])
            ->whereHas('orderDriver', function ($q) use ($userId) {
                $q->where('driver_id', $userId);
            });

        // Apply status filter if provided
        if ($status) {
            $deliveredQuery->where('status', $status);
        }

        $deliveredOrders = $deliveredQuery->orderBy('created_at', 'desc')->get();

        return [
            'created' => $createdOrders,
            'delivered' => $deliveredOrders,
        ];
    }

    /**
     * Add points to user's account.
     *
     * @throws \Exception
     */
    /**
     * Generate a unique 6-digit barcode for orders.
     *
     * @throws \Exception
     */
    private function generateUniqueBarcode(): string
    {
        $maxAttempts = 100; // Prevent infinite loops
        $attempt = 0;

        do {
            // Generate a random 6-digit number
            $barcode = str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);

            // Check if barcode already exists
            $exists = Order::where('barcode', $barcode)->exists();

            if (!$exists) {
                return $barcode;
            }

            ++$attempt;
        } while ($attempt < $maxAttempts);

        throw new \Exception('Unable to generate a unique barcode after '.$maxAttempts.' attempts');
    }

    /**
     * Add points to user's account.
     *
     * @param int      $userId      The ID of the user to add points to
     * @param string   $type        The type of points (e.g., 'order_completed')
     * @param float    $amount      The monetary amount to calculate points from
     * @param string   $description Description of the points transaction
     * @param int|null $orderId     Optional order ID associated with the points
     *
     * @throws \Exception
     */
    private function givePoint(int $userId, string $type, float $amount, string $description, ?int $orderId = null): array
    {
        return \DB::transaction(function () use ($userId, $amount, $description, $orderId) {
            // Find the user
            $user = User::find($userId);
            if (!$user) {
                throw new \Exception(__('user_not_found'));
            }

            // Get points rate from settings
            $pointsRate = Setting::where('key', 'cash_to_points_rate')->first();
            if (!$pointsRate || !is_numeric($pointsRate->value)) {
                throw new \Exception(__('invalid_points_rate'));
            }

            // Calculate points to add
            $points = (float) $pointsRate->value;
            $pointsToAdd = (int) ($amount * $points);

            if ($pointsToAdd <= 0) {
                throw new \Exception(__('invalid_points_amount'));
            }

            // Get or create user points record
            $userPoint = $user->point()->first();

            // Calculate new balances
            $oldBalance = $userPoint ? $userPoint->current_balance : 0;
            $oldTotalEarned = $userPoint ? $userPoint->total_earned : 0;
            $newBalance = $oldBalance + $pointsToAdd;
            $newTotalEarned = $oldTotalEarned + $pointsToAdd;

            // Update or create user point record
            $userPoint = $user->point()->updateOrCreate(
                ['user_id' => $userId],
                [
                    'current_balance' => $newBalance,
                    'total_earned' => $newTotalEarned,
                    // Don't store type and description in UserPoint as they belong to the transaction
                ]
            );

            // Create transaction history
            $transaction = PointTransaction::create([
                'user_id' => $userId,
                'type' => 'earned',
                'amount' => $pointsToAdd,
                'description' => $description,
                'order_id' => $orderId,
            ]);

            if (!$transaction) {
                throw new \Exception(__('failed_to_create_transaction'));
            }

            return [
                'success' => true,
                'points_added' => $pointsToAdd,
                'new_balance' => $newBalance,
                'transaction_id' => $transaction->id,
            ];
        });
    }

    /*
     * Generate a unique 6-digit barcode for orders.
     *
     * @throws \Exception
     */
    // public function generateUniqueBarcode(): string
    // {
    //     $maxAttempts = 100; // Prevent infinite loops
    //     $attempt = 0;

    //     do {
    //         // Generate a random 6-digit number
    //         $barcode = str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);

    //         // Check if barcode already exists
    //         $exists = Order::where('barcode', $barcode)->exists();

    //         if (!$exists) {
    //             return $barcode;
    //         }

    //         ++$attempt;
    //     } while ($attempt < $maxAttempts);

    //     throw new \Exception('Unable to generate a unique barcode after '.$maxAttempts.' attempts');
    // }
}
