<?php

namespace App\Http\Requests\Api;

use App\Models\Pricing;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class CreateOrderRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'from_station_id' => 'required|exists:stations,id',
            'to_station_id' => 'required|exists:stations,id|different:from_station_id',
            'type_id' => 'required|exists:types,id',
            'receiver_name' => 'required|string|max:255',
            'receiver_phone' => 'required|string|max:20',
            'note' => 'nullable|string',
            'price' => 'required|numeric|min:0',
        ];
    }

    public function withValidator(Validator $validator)
    {
        $validator->after(function ($validator) {
            $pricing = Pricing::where([
                'from_station_id' => $this->from_station_id,
                'to_station_id' => $this->to_station_id,
                'type_id' => $this->type_id,
            ])->first();

            if (!$pricing) {
                $validator->errors()->add('pricing', __('no_pricing_available_for_selected_route'));
                return;
            }

            if ($this->price != $pricing->price) {
                $validator->errors()->add('price', __('price_does_not_match_pricing_table'));
            }
        });
    }
}