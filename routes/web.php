<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');
    
    // Wallet Routes
    Route::get('/wallet', \App\Http\Controllers\Web\Wallet\Index::class)->name('wallet.index');
    
    Route::get('/users', \App\Http\Controllers\Web\Users\Index::class)->name('users.index');
    Route::get('/stations', \App\Http\Controllers\Web\Stations\Index::class)->name('stations.index');
    Route::get('/stations/create', \App\Http\Controllers\Web\Stations\Create::class)->name('stations.create');
    Route::get('/stations/{id}/edit', \App\Http\Controllers\Web\Stations\Edit::class)->name('stations.edit');
    Route::get('/types', \App\Http\Controllers\Web\Types\Index::class)->name('types.index');
    Route::get('/pricings', \App\Http\Controllers\Web\Pricings\Index::class)->name('pricings.index');
    Route::get('/pricings/create', \App\Http\Controllers\Web\Pricings\Create::class)->name('pricings.create');
    Route::get('/pricings/{fromStationId}/{toStationId}/edit', \App\Http\Controllers\Web\Pricings\Edit::class)->name('pricings.edit');
    Route::get('/pricings/{fromStationId}/{toStationId}', \App\Http\Controllers\Web\Pricings\Show::class)->name('pricings.show');
    Route::get('/languages', \App\Http\Controllers\Web\Languages\Index::class)->name('languages.index');
    Route::get('/roles', \App\Http\Controllers\Web\Roles\Index::class)->name('roles.index');
    Route::get('/settings', \App\Http\Controllers\Web\Settings\Index::class)->name('settings.index');
});

Route::get('/language/{locale}', [App\Http\Controllers\LanguageController::class, 'switch'])->name('language.switch');
Route::post('/language/set-default/{code}', [App\Http\Controllers\LanguageController::class, 'setDefault'])->name('language.setDefault');
