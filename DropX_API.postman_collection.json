{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "DropX API - Authentication", "description": "مجموعة API للمصادقة في نظام DropX", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"أحم<PERSON> محمد\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"966501234567\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}}}, {"name": "Verify Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"966501234567\",\n    \"code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/verify-code", "host": ["{{base_url}}"], "path": ["api", "auth", "verify-code"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"966501234567\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "Resend Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"966501234567\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/resend-code", "host": ["{{base_url}}"], "path": ["api", "auth", "resend-code"]}}}, {"name": "Get User Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/me", "host": ["{{base_url}}"], "path": ["api", "auth", "me"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/logout", "host": ["{{base_url}}"], "path": ["api", "auth", "logout"]}}}]}], "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000"}, {"key": "auth_token", "value": ""}]}