<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء الصلاحيات
        $permissions = [
            // صلاحيات المستخدمين
            'users.view',
            'users.create', 
            'users.edit',
            'users.delete',
            
            // صلاحيات الأدوار
            'roles.view',
            'roles.create',
            'roles.edit', 
            'roles.delete',
            
            // صلاحيات اللغات
            'languages.view',
            'languages.create',
            'languages.edit',
            'languages.delete',
            
            // صلاحيات النظام
            'dashboard.view',
            'settings.view',
            'settings.edit',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // إنشاء دور الأدمن مع جميع الصلاحيات
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->syncPermissions($permissions);

        // إنشاء دور المستخدم مع صلاحيات محدودة
        $userRole = Role::firstOrCreate(['name' => 'user']);
        $userRole->syncPermissions([
            'dashboard.view',
        ]);

        // إنشاء دور المحرر مع صلاحيات متوسطة
        $editorRole = Role::firstOrCreate(['name' => 'editor']);
        $editorRole->syncPermissions([
            'dashboard.view',
            'users.view',
            'languages.view',
            'languages.create',
            'languages.edit',
        ]);
    }
}
