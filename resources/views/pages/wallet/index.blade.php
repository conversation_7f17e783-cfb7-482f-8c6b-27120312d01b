<div>
    <main class="nxl-container">
        <div class="nxl-content">
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('wallet') }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('home') }}</a></li>
                        <li class="breadcrumb-item">{{ __('wallet') }}</li>
                    </ul>
                </div>
            </div>

            <div class="main-content">
                <!-- Wallet Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h6 class="text-uppercase">{{ __('current_balance') }}</h6>
                                <h2 class="mb-0">{{ number_format($balance) }} <small>{{ __('point') }}</small></h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h6 class="text-uppercase">{{ __('total_earned') }}</h6>
                                <h2 class="mb-0">{{ number_format($totalEarned) }} <small>{{ __('point') }}</small></h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <h6 class="text-uppercase">{{ __('total_spent') }}</h6>
                                <h2 class="mb-0">{{ number_format($totalSpent) }} <small>{{ __('point') }}</small></h2>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-12">
                        <div class="card stretch stretch-full">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('transactions') }}</h5>
                                <div class="card-header-action">
                                    <div class="card-header-btn">
                                        <div class="dropdown">
                                            <a class="btn btn-sm btn-icon btn-light-brand" data-bs-toggle="dropdown">
                                                <i class="feather-more-vertical"></i>
                                            </a>
                                            <div class="dropdown-menu dropdown-menu-end">
                                                <a href="javascript:void(0);" class="dropdown-item">
                                                    <i class="feather-download me-3"></i>
                                                    <span>{{ __('Export') }}</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <!-- Filters -->
                                <div class="p-3 border-bottom">
                                    <form wire:submit.prevent="$refresh" class="row g-3">
                                        <div class="col-md-3">
                                            <label class="form-label">{{ __('transaction_type') }}</label>
                                            <select class="form-select" wire:model="transactionType">
                                                <option value="">{{ __('all') }}</option>
                                                <option value="earned">{{ __('earned') }}</option>
                                                <option value="spent">{{ __('spent') }}</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">{{ __('from_date') }}</label>
                                            <input type="date" class="form-control" wire:model="dateFrom">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">{{ __('to_date') }}</label>
                                            <input type="date" class="form-control" wire:model="dateTo">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">{{ __('search') }}</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" wire:model.debounce.300ms="search" 
                                                       placeholder="{{ __('search') }}...">
                                                <button class="btn btn-primary" type="submit">
                                                    <i class="feather-search"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-hover table-bordered mb-0 text-center">
                                        <thead>
                                            <tr>
                                                <th class="text-center">{{ __('date') }}</th>
                                                <th class="text-center">{{ __('description') }}</th>
                                                <th class="text-center">{{ __('type') }}</th>
                                                <th class="text-center">{{ __('amount') }}</th>
                                                <th class="text-center">{{ __('balance') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($transactions as $transaction)
                                                <tr>
                                                    <td class="text-center">{{ $transaction->created_at->format('Y-m-d H:i') }}</td>
                                                    <td class="text-center">{{ $transaction->description }}</td>
                                                    <td class="text-center">
                                                        @if($transaction->type === 'earned')
                                                            <span class="badge bg-soft-success text-success">{{ __('earned') }}</span>
                                                        @else
                                                            <span class="badge bg-soft-danger text-danger">{{ __('spent') }}</span>
                                                        @endif
                                                    </td>
                                                    <td class="text-center {{ $transaction->type === 'earned' ? 'text-success' : 'text-danger' }}">
                                                        {{ $transaction->type === 'earned' ? '+' : '-' }} {{ number_format($transaction->amount) }}
                                                    </td>
                                                    <td class="text-center">{{ number_format($transaction->balance_after) }}</td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="5" class="text-center py-4">
                                                        <div class="d-flex flex-column align-items-center">
                                                            <i class="feather-inbox font-xxl text-muted mb-2"></i>
                                                            <p class="text-muted mb-0">{{ __('no_transactions_found') }}</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                                
                                @if($transactions->hasPages())
                                    <div class="p-3 border-top">
                                        {{ $transactions->links() }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>
