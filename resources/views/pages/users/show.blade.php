@php
    $user = app(\App\Services\UserService::class)->getUserById($selectedUserId);
@endphp

<div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('User Details') }}</h5>
                <button type="button" class="btn-close" wire:click="closeModal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="d-flex align-items-center gap-4 mb-4">
                            <div class="avatar-image avatar-xl">
                                <img src="{{ asset('assets/images/avatar/1.png') }}" alt="" class="img-fluid">
                            </div>
                            <div>
                                <h4 class="fw-bold text-dark mb-1">{{ $user->name }}</h4>
                                <p class="fs-12 fw-normal text-muted mb-0">{{ $user->email }}</p>
                                @if($user->roles->count() > 0)
                                    <span class="badge bg-soft-primary text-primary mt-2">{{ $user->roles->first()->name }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-4">
                            <label class="form-label fw-semibold text-dark">{{ __('Name') }}</label>
                            <p class="form-control-plaintext">{{ $user->name }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-4">
                            <label class="form-label fw-semibold text-dark">{{ __('Email') }}</label>
                            <p class="form-control-plaintext">{{ $user->email }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-4">
                            <label class="form-label fw-semibold text-dark">{{ __('Role') }}</label>
                            <p class="form-control-plaintext">
                                @if($user->roles->count() > 0)
                                    <span class="badge bg-soft-primary text-primary">{{ $user->roles->first()->name }}</span>
                                @else
                                    <span class="badge bg-soft-secondary text-secondary">{{ __('No Role') }}</span>
                                @endif
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-4">
                            <label class="form-label fw-semibold text-dark">{{ __('Email Verified') }}</label>
                            <p class="form-control-plaintext">
                                @if($user->email_verified_at)
                                    <span class="badge bg-soft-success text-success">{{ __('Verified') }}</span>
                                @else
                                    <span class="badge bg-soft-warning text-warning">{{ __('Not Verified') }}</span>
                                @endif
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-4">
                            <label class="form-label fw-semibold text-dark">{{ __('Created At') }}</label>
                            <p class="form-control-plaintext">{{ $user->created_at->format('Y-m-d H:i:s') }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-4">
                            <label class="form-label fw-semibold text-dark">{{ __('Updated At') }}</label>
                            <p class="form-control-plaintext">{{ $user->updated_at->format('Y-m-d H:i:s') }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" wire:click="closeModal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-primary" wire:click="edit({{ $user->id }})">{{ __('Edit') }}</button>
            </div>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>