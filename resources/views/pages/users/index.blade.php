<div>
    <main class="nxl-container">
        <div class="nxl-content">
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('users') }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('home') }}</a></li>
                        <li class="breadcrumb-item">{{ __('users') }}</li>
                    </ul>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <a href="javascript:void(0);" class="btn btn-primary" wire:click="create">
                                <i class="feather-plus me-2"></i>
                                {{ __('add_user') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card stretch stretch-full">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('users_list') }}</h5>
                                <div class="card-header-action">
                                    <div class="card-header-btn">
                                        <div class="dropdown">
                                            <a class="btn btn-sm btn-icon btn-light-brand" data-bs-toggle="dropdown">
                                                <i class="feather-more-vertical"></i>
                                            </a>
                                            <div class="dropdown-menu dropdown-menu-end">
                                                <a href="javascript:void(0);" class="dropdown-item">
                                                    <i class="feather-download me-3"></i>
                                                    <span>{{ __('Export') }}</span>
                                                </a>
                                                <a href="javascript:void(0);" class="dropdown-item">
                                                    <i class="feather-printer me-3"></i>
                                                    <span>{{ __('Print') }}</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body custom-card-action p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover table-bordered mb-0 text-center">
                                        <thead>
                                            <tr>
                                                <th class="test-center">{{ __('name') }}</th>
                                                <th class="test-center">{{ __('email') }}</th>
                                                <th class="test-center">{{ __('Role') }}</th>
                                                <th class="test-center">{{ __('created_at') }}</th>
                                                <th class="test-center">{{ __('actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($users as $user)
                                                <tr>
                                                    <td class="test-center">
                                                        <div class="d-flex align-items-center gap-3">
                                                            <div class="avatar-image avatar-md">
                                                                <img src="{{ asset('assets/images/avatar/1.png') }}"
                                                                    alt="" class="img-fluid">
                                                            </div>
                                                            <div>
                                                                <div class="fw-bold text-dark">{{ $user->name }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="test-center">{{ $user->email }}</td>
                                                    <td class="test-center">
                                                        @if ($user->roles->count() > 0)
                                                            <span
                                                                class="badge bg-soft-primary text-primary">{{ $user->roles->first()->name }}</span>
                                                        @else
                                                            <span
                                                                class="badge bg-soft-secondary text-secondary">{{ __('No Role') }}</span>
                                                        @endif
                                                    </td>
                                                    <td class="test-center">{{ $user->created_at->format('Y-m-d') }}</td>
                                                    <td class="test-center">
                                                        <div class="hstack gap-2 justify-content-center">
                                                            <a href="javascript:void(0);" class="avatar-text avatar-md"
                                                                wire:click="show({{ $user->id }})">
                                                                <i class="feather-eye"></i>
                                                            </a>
                                                            <a href="javascript:void(0);" class="avatar-text avatar-md"
                                                                wire:click="edit({{ $user->id }})">
                                                                <i class="feather-edit-3"></i>
                                                            </a>
                                                            <a href="javascript:void(0);" class="avatar-text avatar-md"
                                                                wire:click="confirmDelete({{ $user->id }})">
                                                                <i class="feather-trash-2"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="5" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="feather-users fs-1 mb-3"></i>
                                                            <p>{{ __('no_data') }}</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            @if ($users->hasPages())
                                <div class="card-footer">
                                    {{ $users->links() }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            @if ($showCreateModal)
                @include('pages.users.create')
            @endif

            @if ($showEditModal)
                @include('pages.users.update')
            @endif

            @if ($showViewModal)
                @include('pages.users.show')
            @endif

            @if ($showDeleteModal)
                @include('pages.users.delete')
            @endif
        </div>
    </main>
</div>
