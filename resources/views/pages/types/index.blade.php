<div>
    <main class="nxl-container">
        <div class="nxl-content">
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('types') }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('home') }}</a></li>
                        <li class="breadcrumb-item">{{ __('types') }}</li>
                    </ul>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" wire:model.live="showDeleted" id="showDeleted">
                                <label class="form-check-label" for="showDeleted">
                                    {{ __('show_deleted') }}
                                </label>
                            </div>
                            <a href="javascript:void(0);" class="btn btn-primary" wire:click="create">
                                <i class="feather-plus me-2"></i>
                                {{ __('add_type') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card stretch stretch-full">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('types_list') }}</h5>
                            </div>
                            <div class="card-body custom-card-action p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover table-bordered mb-0 text-center">
                                        <thead>
                                            <tr>
                                                <th>{{ __('name') }}</th>
                                                <th>{{ __('actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($types as $type)
                                                <tr>
                                                    <td>{{ $type->name }}</td>
                                                    <td>
                                                        <div class="hstack gap-2 justify-content-center">
                                                            @if($type->trashed())
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md text-success" wire:click="confirmRestore({{ $type->id }})" title="{{ __('restore') }}">
                                                                    <i class="feather-refresh-cw"></i>
                                                                </a>
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md text-danger" wire:click="confirmForceDelete({{ $type->id }})" title="{{ __('permanent_delete') }}">
                                                                    <i class="feather-x"></i>
                                                                </a>
                                                            @else
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="edit({{ $type->id }})">
                                                                    <i class="feather-edit-3"></i>
                                                                </a>
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="confirmDelete({{ $type->id }})">
                                                                    <i class="feather-trash-2"></i>
                                                                </a>
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="2" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="feather-tag fs-1 mb-3"></i>
                                                            <p>{{ __('no_data') }}</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            @if ($types->hasPages())
                                <div class="card-footer">
                                    {{ $types->links() }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            @if ($showCreateModal)
                @include('pages.types.create')
            @endif

            @if ($showEditModal)
                @include('pages.types.update')
            @endif

            @if ($showDeleteModal)
                @include('pages.types.delete')
            @endif

            @if ($showRestoreModal)
                @include('pages.types.restore')
            @endif

            @if ($showForceDeleteModal)
                @include('pages.types.force-delete')
            @endif
        </div>
    </main>
</div>