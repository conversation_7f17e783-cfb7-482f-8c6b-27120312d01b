<main class="nxl-container">
    <div class="nxl-content">
        <!-- [ page-header ] start -->
        <div class="page-header">
            <div class="page-header-left d-flex align-items-center">
                <div class="page-header-title">
                    <h5 class="m-b-10">{{ __('edit_station') }}</h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('dashboard') }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('stations.index') }}">{{ __('stations') }}</a></li>
                    <li class="breadcrumb-item">{{ __('edit_station') }}</li>
                </ul>
            </div>
        </div>
        <!-- [ page-header ] end -->
        <!-- [ Main Content ] start -->
        <div class="main-content">
            <div class="row">
                <div class="col-12">
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">{{ __('edit_station') }}</h5>
                        </div>
                        <div class="card-body">
                            <form wire:submit.prevent="update">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-4">
                                            <label class="form-label">{{ __('name') }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" wire:model="name" placeholder="{{ __('name') }}">
                                            @error('name') <span class="text-danger">{{ $message }}</span> @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label class="form-label">{{ __('latitude') }} <span class="text-danger">*</span></label>
                                            <input type="number" step="any" class="form-control" wire:model="latitude" id="latitude" placeholder="{{ __('latitude') }}">
                                            @error('latitude') <span class="text-danger">{{ $message }}</span> @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label class="form-label">{{ __('longitude') }} <span class="text-danger">*</span></label>
                                            <input type="number" step="any" class="form-control" wire:model="longitude" id="longitude" placeholder="{{ __('longitude') }}">
                                            @error('longitude') <span class="text-danger">{{ $message }}</span> @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div id="map" style="width:100%; height:600px;"></div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-end gap-2 mt-3">
                                    <a href="{{ route('stations.index') }}" class="btn btn-light">{{ __('cancel') }}</a>
                                    <button type="submit" class="btn btn-primary">{{ __('update') }}</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ Main Content ] end -->
    </div>
</main>

<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyC0dpNtSqMSIiRVX783bVJOpQILZEF09Eo&libraries=places"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let map;
        let marker;
        
        function initMap() {
            let centerOfMap = new google.maps.LatLng(31.748553083918008, 35.217695259591714);
            
            let options = {
                center: centerOfMap,
                zoom: 12,
                mapTypeId: google.maps.MapTypeId.ROADMAP
            };
            
            map = new google.maps.Map(document.getElementById('map'), options);
            
            function updateInputsAndTriggerLivewire(location) {
                let lat = location.lat();
                let lng = location.lng();
                
                document.getElementById('latitude').value = lat;
                document.getElementById('longitude').value = lng;
                
                // Update Livewire properties silently without re-render
                @this.latitude = lat;
                @this.longitude = lng;
            }
            
            function handleMarkerCreation(event) {
                let clickedLocation = event.latLng;
                
                if (marker) {
                    marker.setMap(null);
                }
                
                marker = new google.maps.Marker({
                    position: clickedLocation,
                    map: map,
                    draggable: true,
                    animation: google.maps.Animation.DROP
                });
                
                updateInputsAndTriggerLivewire(clickedLocation);
                
                google.maps.event.addListener(marker, 'dragend', function(event) {
                    updateInputsAndTriggerLivewire(marker.getPosition());
                });
            }
            
            google.maps.event.addListener(map, 'click', handleMarkerCreation);
            
            let existingLat = document.getElementById('latitude').value;
            let existingLng = document.getElementById('longitude').value;
            
            if (existingLat && existingLng) {
                let existingPosition = new google.maps.LatLng(parseFloat(existingLat), parseFloat(existingLng));
                marker = new google.maps.Marker({
                    position: existingPosition,
                    map: map,
                    draggable: true
                });
                
                map.setCenter(existingPosition);
                
                google.maps.event.addListener(marker, 'dragend', function(event) {
                    updateInputsAndTriggerLivewire(marker.getPosition());
                });
            }
        }
        
        initMap();
    });
</script>