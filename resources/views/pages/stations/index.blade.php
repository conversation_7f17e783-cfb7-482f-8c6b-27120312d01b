<div>
    <main class="nxl-container">
        <div class="nxl-content">
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('stations') }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('home') }}</a></li>
                        <li class="breadcrumb-item">{{ __('stations') }}</li>
                    </ul>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" wire:model.live="showDeleted" id="showDeleted">
                                <label class="form-check-label" for="showDeleted">
                                    {{ __('show_deleted') }}
                                </label>
                            </div>
                            <a href="javascript:void(0);" class="btn btn-primary" wire:click="create">
                                <i class="feather-plus me-2"></i>
                                {{ __('add_station') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card stretch stretch-full">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('stations_list') }}</h5>
                            </div>
                            <div class="card-body custom-card-action p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover table-bordered mb-0 text-center">
                                        <thead>
                                            <tr>
                                                <th>{{ __('name') }}</th>
                                                <th>{{ __('latitude') }}</th>
                                                <th>{{ __('longitude') }}</th>
                                                <th>{{ __('actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($stations as $station)
                                                <tr>
                                                    <td>{{ $station->name }}</td>
                                                    <td>{{ $station->latitude }}</td>
                                                    <td>{{ $station->longitude }}</td>
                                                    <td>
                                                        <div class="hstack gap-2 justify-content-center">
                                                            @if($station->trashed())
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md text-success" wire:click="confirmRestore({{ $station->id }})" title="{{ __('restore') }}">
                                                                    <i class="feather-refresh-cw"></i>
                                                                </a>
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md text-danger" wire:click="confirmForceDelete({{ $station->id }})" title="{{ __('permanent_delete') }}">
                                                                    <i class="feather-x"></i>
                                                                </a>
                                                            @else
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="edit({{ $station->id }})">
                                                                    <i class="feather-edit-3"></i>
                                                                </a>
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="confirmDelete({{ $station->id }})">
                                                                    <i class="feather-trash-2"></i>
                                                                </a>
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="4" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="feather-map-pin fs-1 mb-3"></i>
                                                            <p>{{ __('no_data') }}</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            @if ($stations->hasPages())
                                <div class="card-footer">
                                    {{ $stations->links() }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            @if ($showCreateModal)
                @include('pages.stations.create')
            @endif

            @if ($showEditModal)
                @include('pages.stations.update')
            @endif

            @if ($showDeleteModal)
                @include('pages.stations.delete')
            @endif

            @if ($showRestoreModal)
                @include('pages.stations.restore')
            @endif

            @if ($showForceDeleteModal)
                @include('pages.stations.force-delete')
            @endif
        </div>
    </main>
</div>