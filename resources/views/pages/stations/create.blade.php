<main class="nxl-container">
    <div class="nxl-content">
        <!-- [ page-header ] start -->
        <div class="page-header">
            <div class="page-header-left d-flex align-items-center">
                <div class="page-header-title">
                    <h5 class="m-b-10">{{ __('add_station') }}</h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('dashboard') }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('stations.index') }}">{{ __('stations') }}</a></li>
                    <li class="breadcrumb-item">{{ __('add_station') }}</li>
                </ul>
            </div>
        </div>
        <!-- [ page-header ] end -->
        <!-- [ Main Content ] start -->
        <div class="main-content">
            <div class="row">
                <div class="col-12">
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">{{ __('add_station') }}</h5>
                        </div>
                        <div class="card-body">
                            <form wire:submit.prevent="store">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-4">
                                            <label class="form-label">{{ __('name') }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" wire:model="name" placeholder="{{ __('name') }}">
                                            @error('name')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label class="form-label">{{ __('latitude') }} <span class="text-danger">*</span></label>
                                            <input type="number" step="any" class="form-control" wire:model="latitude" id="latitude" placeholder="{{ __('latitude') }}">
                                            @error('latitude')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label class="form-label">{{ __('longitude') }} <span class="text-danger">*</span></label>
                                            <input type="number" step="any" class="form-control" wire:model="longitude" id="longitude" placeholder="{{ __('longitude') }}">
                                            @error('longitude')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div id="map" style="width:100%; height:600px;"></div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-end gap-2 mt-3">
                                    <a href="{{ route('stations.index') }}" class="btn btn-light">{{ __('cancel') }}</a>
                                    <button type="submit" class="btn btn-primary">{{ __('save') }}</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ Main Content ] end -->
    </div>
</main>
@push('js')
<script async defer
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyC0dpNtSqMSIiRVX783bVJOpQILZEF09Eo&callback=initMap"
    type="text/javascript"></script>
<script>
    let map;
    let marker = false;

    function initMap() {
        let centerOfMap = new google.maps.LatLng(31.748553083918008, 35.217695259591714);

        let options = {
            center: centerOfMap,
            zoom: 12,
            lang: 'ar'
        };
        map = new google.maps.Map(document.getElementById('map'), options);

        let marker;

        function updateInputsAndLogValues(location) {
            let lat = location.lat();
            let lng = location.lng();
            
            // Update input values without triggering Livewire re-render
            document.getElementById('latitude').value = lat;
            document.getElementById('longitude').value = lng;
            
            // Update Livewire properties silently
            @this.latitude = lat;
            @this.longitude = lng;
        }

        function handleMarkerCreation(event) {
            let clickedLocation = event.latLng;
            updateInputsAndLogValues(clickedLocation);
            if (marker) {
                marker.setMap(null);
            }
            marker = new google.maps.Marker({
                position: clickedLocation,
                map: map,
                draggable: true
            });

            google.maps.event.addListener(marker, 'dragend', function(event) {
                let currentLocation = marker.getPosition();
                updateInputsAndLogValues(currentLocation);
            });
        }
        google.maps.event.addListener(map, 'click', handleMarkerCreation);
    }
</script>
@endpush
