<div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('view_pricing') }}</h5>
                <button type="button" class="btn-close" wire:click="closeModal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">{{ __('from_station') }}</label>
                            <p class="form-control-plaintext">
                                {{ collect($stations)->where('id', $from_station_id)->first()->name ?? '' }}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">{{ __('to_station') }}</label>
                            <p class="form-control-plaintext">
                                {{ collect($stations)->where('id', $to_station_id)->first()->name ?? '' }}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">{{ __('prices_by_type') }}</label>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>{{ __('type') }}</th>
                                    <th>{{ __('price') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($types as $type)
                                    @if(isset($prices[$type->id]) && $prices[$type->id] > 0)
                                        <tr>
                                            <td>{{ $type->name }}</td>
                                            <td>{{ number_format($prices[$type->id], 2) }}</td>
                                        </tr>
                                    @endif
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" wire:click="closeModal">{{ __('close') }}</button>
            </div>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>