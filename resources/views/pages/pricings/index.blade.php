<main class="nxl-container">
        <div class="nxl-content">
            <!-- [ page-header ] start -->
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('pricings') }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('dashboard') }}</a></li>
                        <li class="breadcrumb-item">{{ __('pricings') }}</li>
                    </ul>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex d-md-none">
                            <a href="javascript:void(0)" class="page-header-right-close-toggle">
                                <i class="feather-arrow-left me-2"></i>
                                <span>{{ __('back') }}</span>
                            </a>
                        </div>
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" wire:model.live="showDeleted" id="showDeleted">
                                <label class="form-check-label" for="showDeleted">
                                    {{ __('show_deleted') }}
                                </label>
                            </div>
                            <a href="javascript:void(0);" class="btn btn-primary" wire:click="create">
                                <i class="feather-plus me-2"></i>
                                {{ __('add_pricing') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <!-- [ page-header ] end -->
            <!-- [ Main Content ] start -->
            <div class="main-content">
                <div class="row">
                    <div class="col-12">
                        <div class="card stretch stretch-full">
                            <div class="card-body custom-card-action p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr class="border-b">
                                                <th>{{ __('from_station') }}</th>
                                                <th>{{ __('to_station') }}</th>
                                                <th>{{ __('types_count') }}</th>
                                                <th class="text-end">{{ __('actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($pricings as $pricing)
                                                <tr class="single-item @if($pricing->deleted_at) table-warning @endif">
                                                    <td>{{ $pricing->fromStation->name }}</td>
                                                    <td>{{ $pricing->toStation->name }}</td>
                                                    <td>
                                                        <span class="badge bg-primary">{{ $pricing->types_count }} {{ __('types') }}</span>
                                                    </td>
                                                    <td>
                                                        <div class="hstack gap-2 justify-content-center">
                                                            @if($pricing->deleted_at)
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md text-success" wire:click="confirmRestoreRoute({{ $pricing->from_station_id }}, {{ $pricing->to_station_id }})" title="{{ __('restore') }}">
                                                                    <i class="feather-refresh-cw"></i>
                                                                </a>
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md text-danger" wire:click="confirmForceDeleteRoute({{ $pricing->from_station_id }}, {{ $pricing->to_station_id }})" title="{{ __('permanent_delete') }}">
                                                                    <i class="feather-x"></i>
                                                                </a>
                                                            @else
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="viewRoute({{ $pricing->from_station_id }}, {{ $pricing->to_station_id }})" title="{{ __('view') }}">
                                                                    <i class="feather-eye"></i>
                                                                </a>
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="editRoute({{ $pricing->from_station_id }}, {{ $pricing->to_station_id }})" title="{{ __('edit') }}">
                                                                    <i class="feather-edit-3"></i>
                                                                </a>
                                                                <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="confirmDeleteRoute({{ $pricing->from_station_id }}, {{ $pricing->to_station_id }})" title="{{ __('delete') }}">
                                                                    <i class="feather-trash-2"></i>
                                                                </a>
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="4" class="text-center">{{ __('no_data_found') }}</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-center">
                    {{ $pricings->links() }}
                </div>

        @if ($showCreateModal)
            @include('pages.pricings.create')
        @endif

        @if ($showEditModal)
            @include('pages.pricings.update')
        @endif

        @if ($showDeleteModal)
            @include('pages.pricings.delete')
        @endif

        @if ($showRestoreModal)
            @include('pages.pricings.restore')
        @endif

            @if ($showCreateModal)
                @include('pages.pricings.create')
            @endif

            @if ($showEditModal)
                @include('pages.pricings.update')
            @endif

            @if ($showDeleteModal)
                @include('pages.pricings.delete')
            @endif

            @if ($showRestoreModal)
                @include('pages.pricings.restore')
            @endif

            @if ($showForceDeleteModal)
                @include('pages.pricings.force-delete')
            @endif

            @if ($showViewModal)
                @include('pages.pricings.view')
            @endif
            </div>
            <!-- [ Main Content ] end -->
        </div>
    </main>