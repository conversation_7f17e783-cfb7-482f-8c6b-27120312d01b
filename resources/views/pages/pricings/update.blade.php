<div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('edit_pricing') }}</h5>
                <button type="button" class="btn-close" wire:click="closeModal"></button>
            </div>
            <div class="modal-body">
                <form wire:submit.prevent="update">
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="from_station_id" class="form-label">{{ __('from_station') }}</label>
                                <select class="form-select" wire:model.live="from_station_id" id="from_station_id">
                                    <option value="">{{ __('select_station') }}</option>
                                    @foreach($stations as $station)
                                        @if($station->id != $to_station_id)
                                            <option value="{{ $station->id }}">{{ $station->name }}</option>
                                        @endif
                                    @endforeach
                                </select>
                                @error('from_station_id') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="to_station_id" class="form-label">{{ __('to_station') }}</label>
                                <select class="form-select" wire:model.live="to_station_id" id="to_station_id">
                                    <option value="">{{ __('select_station') }}</option>
                                    @foreach($stations as $station)
                                        @if($station->id != $from_station_id)
                                            <option value="{{ $station->id }}">{{ $station->name }}</option>
                                        @endif
                                    @endforeach
                                </select>
                                @error('to_station_id') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="type_id" class="form-label">{{ __('type') }}</label>
                        <select class="form-select" wire:model="type_id" id="type_id">
                            <option value="">{{ __('select_type') }}</option>
                            @foreach($types as $type)
                                <option value="{{ $type->id }}">{{ $type->name }}</option>
                            @endforeach
                        </select>
                        @error('type_id') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>

                    <div class="mb-3">
                        <label for="price" class="form-label">{{ __('price') }}</label>
                        <input type="number" class="form-control" wire:model="price" id="price" step="0.01" min="0">
                        @error('price') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" wire:click="closeModal">{{ __('cancel') }}</button>
                <button type="button" class="btn btn-primary" wire:click="update">{{ __('update') }}</button>
            </div>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>