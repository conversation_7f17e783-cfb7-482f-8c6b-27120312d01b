<main class="nxl-container">
    <div class="nxl-content">
        <!-- [ page-header ] start -->
        <div class="page-header">
            <div class="page-header-left d-flex align-items-center">
                <div class="page-header-title">
                    <h5 class="m-b-10">{{ __('edit_pricing') }}</h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('dashboard') }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('pricings.index') }}">{{ __('pricings') }}</a></li>
                    <li class="breadcrumb-item">{{ __('edit_pricing') }}</li>
                </ul>
            </div>
        </div>
        <!-- [ page-header ] end -->
        <!-- [ Main Content ] start -->
        <div class="main-content">
            <div class="row">
                <div class="col-12">
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">{{ __('edit_pricing') }}</h5>
                        </div>
                        <div class="card-body">
                            <form wire:submit.prevent="update">
                                <div class="mb-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">{{ __('from_station') }}</label>
                                            <p class="form-control-plaintext">
                                                {{ collect($stations)->where('id', $from_station_id)->first()->name ?? '' }}
                                            </p>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">{{ __('to_station') }}</label>
                                            <p class="form-control-plaintext">
                                                {{ collect($stations)->where('id', $to_station_id)->first()->name ?? '' }}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">{{ __('prices_by_type') }}</label>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('type') }}</th>
                                                    <th>{{ __('price') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($types as $type)
                                                    <tr>
                                                        <td>{{ $type->name }}</td>
                                                        <td>
                                                            <input type="number" 
                                                                   class="form-control" 
                                                                   wire:model="prices.{{ $type->id }}" 
                                                                   step="0.01" 
                                                                   min="0"
                                                                   placeholder="0.00">
                                                            @error('prices.' . $type->id) 
                                                                <span class="text-danger">{{ $message }}</span> 
                                                            @enderror
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('pricings.index') }}" class="btn btn-light">{{ __('cancel') }}</a>
                                    <button type="submit" class="btn btn-primary">{{ __('update') }}</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- [ Main Content ] end -->
    </div>
</main>