<div class="modal fade show" style="display: block;" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('add_pricing') }}</h5>
                <button type="button" class="btn-close" wire:click="closeModal"></button>
            </div>
            <div class="modal-body">
                <form wire:submit.prevent="store">
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="from_station_id" class="form-label">{{ __('from_station') }}</label>
                                <select class="form-select" wire:model.live="from_station_id" id="from_station_id">
                                    <option value="">{{ __('select_station') }}</option>
                                    @foreach($stations as $station)
                                        @if($station->id != $to_station_id)
                                            <option value="{{ $station->id }}">{{ $station->name }}</option>
                                        @endif
                                    @endforeach
                                </select>
                                @error('from_station_id') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="to_station_id" class="form-label">{{ __('to_station') }}</label>
                                <select class="form-select" wire:model.live="to_station_id" id="to_station_id">
                                    <option value="">{{ __('select_station') }}</option>
                                    @foreach($stations as $station)
                                        @if($station->id != $from_station_id)
                                            <option value="{{ $station->id }}">{{ $station->name }}</option>
                                        @endif
                                    @endforeach
                                </select>
                                @error('to_station_id') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">{{ __('prices_by_type') }}</label>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>{{ __('type') }}</th>
                                        <th>{{ __('price') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($types as $type)
                                        <tr>
                                            <td>{{ $type->name }}</td>
                                            <td>
                                                <input type="number" 
                                                       class="form-control" 
                                                       wire:model="prices.{{ $type->id }}" 
                                                       step="0.01" 
                                                       min="0"
                                                       placeholder="0.00">
                                                @error('prices.' . $type->id) 
                                                    <span class="text-danger">{{ $message }}</span> 
                                                @enderror
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" wire:click="closeModal">{{ __('cancel') }}</button>
                <button type="button" class="btn btn-primary" wire:click="store">{{ __('save') }}</button>
            </div>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>