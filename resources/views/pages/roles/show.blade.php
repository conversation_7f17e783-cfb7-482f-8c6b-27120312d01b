<div class="modal fade show" style="display: block; background: rgba(0,0,0,0.5);" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('view_role') }}</h5>
                <button type="button" class="btn-close" wire:click="closeModal"></button>
            </div>
            <div class="modal-body">
                @if($selectedRoleId)
                    @php $role = $this->roleService->getRoleById($selectedRoleId) @endphp
                    <div class="row">
                        <div class="col-md-6">
                            <strong>{{ __('name') }}:</strong>
                            <p><span class="badge bg-soft-primary text-primary">{{ $role->name }}</span></p>
                        </div>
                        <div class="col-md-6">
                            <strong>{{ __('guard_name') }}:</strong>
                            <p><code>{{ $role->guard_name }}</code></p>
                        </div>
                        <div class="col-md-6">
                            <strong>{{ __('users_count') }}:</strong>
                            <p><span class="badge bg-soft-info text-info">{{ $role->users->count() }}</span></p>
                        </div>
                        <div class="col-md-6">
                            <strong>{{ __('created_at') }}:</strong>
                            <p>{{ $role->created_at->format('Y-m-d H:i') }}</p>
                        </div>
                        <div class="col-12">
                            <strong>{{ __('permissions') }}:</strong>
                            <div class="mt-2">
                                @if($role->permissions->count() > 0)
                                    @php
                                        $rolePermissions = $role->permissions->groupBy(function($permission) {
                                            return explode('.', $permission->name)[0];
                                        });
                                    @endphp
                                    @foreach($rolePermissions as $module => $modulePermissions)
                                        <div class="mb-3">
                                            <h6 class="mb-2">
                                                <i class="feather-{{ $module === 'users' ? 'users' : ($module === 'roles' ? 'shield' : ($module === 'languages' ? 'globe' : 'settings')) }} me-2"></i>
                                                {{ __($module) }}
                                            </h6>
                                            <div>
                                                @foreach($modulePermissions as $permission)
                                                    <span class="badge bg-soft-info text-info me-1 mb-1">{{ __($permission->name) }}</span>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <span class="text-muted">{{ __('no_permissions') }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" wire:click="closeModal">{{ __('close') }}</button>
            </div>
        </div>
    </div>
</div>