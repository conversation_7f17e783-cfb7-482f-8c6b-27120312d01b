<div>
    <main class="nxl-container">
        <div class="nxl-content">
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('roles') }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('home') }}</a></li>
                        <li class="breadcrumb-item">{{ __('roles') }}</li>
                    </ul>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <a href="javascript:void(0);" class="btn btn-primary" wire:click="create">
                                <i class="feather-plus me-2"></i>
                                {{ __('add_role') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card stretch stretch-full">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('roles_list') }}</h5>
                            </div>
                            <div class="card-body custom-card-action p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover table-bordered mb-0 text-center">
                                        <thead>
                                            <tr>
                                                <th>{{ __('name') }}</th>
                                                <th>{{ __('guard_name') }}</th>
                                                <th>{{ __('users_count') }}</th>
                                                <th>{{ __('created_at') }}</th>
                                                <th>{{ __('actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($roles as $role)
                                                <tr>
                                                    <td>
                                                        <span class="badge bg-soft-primary text-primary">{{ $role->name }}</span>
                                                    </td>
                                                    <td><code>{{ $role->guard_name }}</code></td>
                                                    <td>
                                                        <span class="badge bg-soft-info text-info">{{ $role->users->count() }}</span>
                                                    </td>
                                                    <td>{{ $role->created_at->format('Y-m-d') }}</td>
                                                    <td>
                                                        <div class="hstack gap-2 justify-content-center">
                                                            <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="show({{ $role->id }})">
                                                                <i class="feather-eye"></i>
                                                            </a>
                                                            <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="edit({{ $role->id }})">
                                                                <i class="feather-edit-3"></i>
                                                            </a>
                                                            <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="confirmDelete({{ $role->id }})">
                                                                <i class="feather-trash-2"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="5" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="feather-shield fs-1 mb-3"></i>
                                                            <p>{{ __('no_data') }}</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            @if ($roles->hasPages())
                                <div class="card-footer">
                                    {{ $roles->links() }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            @if ($showCreateModal)
                @include('pages.roles.create')
            @endif

            @if ($showEditModal)
                @include('pages.roles.update')
            @endif

            @if ($showViewModal)
                @include('pages.roles.show')
            @endif

            @if ($showDeleteModal)
                @include('pages.roles.delete')
            @endif
        </div>
    </main>
</div>
