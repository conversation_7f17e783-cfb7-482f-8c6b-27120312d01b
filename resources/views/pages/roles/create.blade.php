<div class="modal fade show" style="display: block; background: rgba(0,0,0,0.5);" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('add_role') }}</h5>
                <button type="button" class="btn-close" wire:click="closeModal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">{{ __('name') }}</label>
                        <input type="text" class="form-control" wire:model="name" placeholder="{{ __('enter_role_name') }}">
                        @error('name') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ __('guard_name') }}</label>
                        <select class="form-select" wire:model="guard_name">
                            <option value="web">Web</option>
                            <option value="api">API</option>
                        </select>
                        @error('guard_name') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ __('permissions') }}</label>
                        @foreach($groupedPermissions as $module => $modulePermissions)
                            <div class="card mb-3">
                                <div class="card-header py-2">
                                    <h6 class="mb-0">
                                        <i class="feather-{{ $module === 'users' ? 'users' : ($module === 'roles' ? 'shield' : ($module === 'languages' ? 'globe' : 'settings')) }} me-2"></i>
                                        {{ __($module) }}
                                    </h6>
                                </div>
                                <div class="card-body py-2">
                                    <div class="row">
                                        @foreach($modulePermissions as $permission)
                                            <div class="col-md-6 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" wire:model="permissions" value="{{ $permission['name'] }}" id="perm_{{ $permission['name'] }}">
                                                    <label class="form-check-label" for="perm_{{ $permission['name'] }}">{{ $permission['label'] }}</label>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" wire:click="closeModal">{{ __('cancel') }}</button>
                <button type="button" class="btn btn-primary" wire:click="store">{{ __('save') }}</button>
            </div>
        </div>
    </div>
</div>