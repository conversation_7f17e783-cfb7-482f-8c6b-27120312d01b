<div>
    <main class="nxl-container">
        <div class="nxl-content">
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('settings') }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('home') }}</a></li>
                        <li class="breadcrumb-item">{{ __('settings') }}</li>
                    </ul>
                </div>
            </div>

            <div class="main-content">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card stretch stretch-full">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('site_settings') }}</h5>
                            </div>
                            <div class="card-body">
                                <form wire:submit.prevent="save">
                                    <!-- Navigation Tabs -->
                                    <ul class="nav nav-tabs mb-4" role="tablist">
                                        @foreach($settingGroups as $group)
                                            <li class="nav-item">
                                                <a class="nav-link {{ $activeTab === $group ? 'active' : '' }}" 
                                                   wire:click="setActiveTab('{{ $group }}')" 
                                                   href="javascript:void(0);">
                                                    {{ __($group . '_settings') }}
                                                </a>
                                            </li>
                                        @endforeach
                                    </ul>

                                    <!-- Tab Content -->
                                    <div class="tab-content">
                                        @foreach($groupedSettings as $group => $groupSettings)
                                            <div class="tab-pane {{ $activeTab === $group ? 'show active' : '' }}">
                                                @if($activeTab === $group)
                                                    <div class="row">
                                                        @foreach($groupSettings as $setting)
                                                            <div class="col-md-6 mb-3">
                                                                <label class="form-label">{{ __($setting->key) }}</label>
                                                                @if($setting->type === 'text' || $setting->type === 'email' || $setting->type === 'url' || $setting->type === 'number')
                                                                    <input type="{{ $setting->type }}" 
                                                                           class="form-control" 
                                                                           wire:model="settings.{{ $setting->key }}"
                                                                           placeholder="{{ __($setting->key) }}">
                                                                @elseif($setting->type === 'password')
                                                                    <input type="password" 
                                                                           class="form-control" 
                                                                           wire:model="settings.{{ $setting->key }}"
                                                                           placeholder="{{ __($setting->key) }}">
                                                                @elseif($setting->type === 'textarea')
                                                                    <textarea class="form-control" 
                                                                              wire:model="settings.{{ $setting->key }}"
                                                                              rows="3"
                                                                              placeholder="{{ __($setting->key) }}"></textarea>
                                                                @elseif($setting->type === 'boolean')
                                                                    <div class="form-check form-switch">
                                                                        <input class="form-check-input" 
                                                                               type="checkbox" 
                                                                               wire:model="settings.{{ $setting->key }}"
                                                                               value="1">
                                                                        <label class="form-check-label">{{ __('enable') }}</label>
                                                                    </div>
                                                                @elseif($setting->type === 'file')
                                                                    <input type="file" 
                                                                           class="form-control" 
                                                                           wire:model="settings.{{ $setting->key }}">
                                                                    @if($settings[$setting->key] ?? null)
                                                                        <small class="text-muted">{{ __('current_file') }}: {{ basename($settings[$setting->key]) }}</small>
                                                                    @endif
                                                                @elseif($setting->type === 'select')
                                                                    <select class="form-control" wire:model="settings.{{ $setting->key }}">
                                                                        @if($setting->key === 'mail_driver')
                                                                            <option value="smtp">SMTP</option>
                                                                            <option value="sendmail">Sendmail</option>
                                                                            <option value="mailgun">Mailgun</option>
                                                                        @elseif($setting->key === 'mail_encryption')
                                                                            <option value="tls">TLS</option>
                                                                            <option value="ssl">SSL</option>
                                                                        @elseif($setting->key === 'paypal_mode')
                                                                            <option value="sandbox">Sandbox</option>
                                                                            <option value="live">Live</option>
                                                                        @elseif($setting->key === 'storage_driver')
                                                                            <option value="local">Local</option>
                                                                            <option value="s3">Amazon S3</option>
                                                                        @endif
                                                                    </select>
                                                                @endif
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>

                                    <div class="d-flex justify-content-end mt-4">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="feather-save me-2"></i>
                                            {{ __('save') }}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>