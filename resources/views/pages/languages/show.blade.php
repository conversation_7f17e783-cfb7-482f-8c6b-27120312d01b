<div class="modal fade show" style="display: block; background: rgba(0,0,0,0.5);" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('view_language') }}</h5>
                <button type="button" class="btn-close" wire:click="closeModal"></button>
            </div>
            <div class="modal-body">
                @if($selectedLanguageId)
                    @php $language = $this->languageService->getLanguageById($selectedLanguageId) @endphp
                    <div class="row">
                        <div class="col-md-6">
                            <strong>{{ __('name') }}:</strong>
                            <p>{{ $language->name }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>{{ __('code') }}:</strong>
                            <p><code>{{ $language->code }}</code></p>
                        </div>
                        <div class="col-md-6">
                            <strong>{{ __('flag') }}:</strong>
                            <p style="font-size: 2em;">{{ $language->flag }}</p>
                        </div>
                        <div class="col-md-6">
                            <strong>{{ __('direction') }}:</strong>
                            <p>
                                <span class="badge {{ $language->direction === 'rtl' ? 'bg-soft-warning text-warning' : 'bg-soft-info text-info' }}">
                                    {{ strtoupper($language->direction) }}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <strong>{{ __('status') }}:</strong>
                            <p>
                                <span class="badge {{ $language->is_active ? 'bg-soft-success text-success' : 'bg-soft-danger text-danger' }}">
                                    {{ $language->is_active ? __('active') : __('inactive') }}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <strong>{{ __('created_at') }}:</strong>
                            <p>{{ $language->created_at->format('Y-m-d H:i') }}</p>
                        </div>
                    </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" wire:click="closeModal">{{ __('close') }}</button>
            </div>
        </div>
    </div>
</div>