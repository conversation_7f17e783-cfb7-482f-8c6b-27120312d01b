<div class="modal fade show" style="display: block; background: rgba(0,0,0,0.5);" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('add_language') }}</h5>
                <button type="button" class="btn-close" wire:click="closeModal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">{{ __('name') }}</label>
                        <input type="text" class="form-control" wire:model="name" placeholder="{{ __('enter_language_name') }}">
                        @error('name') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ __('code') }}</label>
                        <input type="text" class="form-control" wire:model="code" placeholder="en, ar, he">
                        @error('code') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ __('flag') }}</label>
                        <input type="text" class="form-control" wire:model="flag" placeholder="🇺🇸">
                        @error('flag') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ __('direction') }}</label>
                        <select class="form-select" wire:model="direction">
                            <option value="ltr">LTR (Left to Right)</option>
                            <option value="rtl">RTL (Right to Left)</option>
                        </select>
                        @error('direction') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" wire:model="is_active" id="is_active">
                            <label class="form-check-label" for="is_active">{{ __('active') }}</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" wire:click="closeModal">{{ __('cancel') }}</button>
                <button type="button" class="btn btn-primary" wire:click="store">{{ __('save') }}</button>
            </div>
        </div>
    </div>
</div>