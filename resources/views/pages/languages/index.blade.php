<div>
    <main class="nxl-container">
        <div class="nxl-content">
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('languages') }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('home') }}</a></li>
                        <li class="breadcrumb-item">{{ __('languages') }}</li>
                    </ul>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <a href="javascript:void(0);" class="btn btn-primary" wire:click="create">
                                <i class="feather-plus me-2"></i>
                                {{ __('add_language') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card stretch stretch-full">
                            <div class="card-header">
                                <h5 class="card-title">{{ __('languages_list') }}</h5>
                            </div>
                            <div class="card-body custom-card-action p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover table-bordered mb-0 text-center">
                                        <thead>
                                            <tr>
                                                <th>{{ __('name') }}</th>
                                                <th>{{ __('code') }}</th>
                                                <th>{{ __('flag') }}</th>
                                                <th>{{ __('direction') }}</th>
                                                <th>{{ __('status') }}</th>
                                                <th>{{ __('default') }}</th>
                                                <th>{{ __('actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($languages as $language)
                                                <tr>
                                                    <td>{{ $language->name }}</td>
                                                    <td><code>{{ $language->code }}</code></td>
                                                    <td><span style="font-size: 1.5em;">{{ $language->flag }}</span></td>
                                                    <td>
                                                        <span class="badge {{ $language->direction === 'rtl' ? 'bg-soft-warning text-warning' : 'bg-soft-info text-info' }}">
                                                            {{ strtoupper($language->direction) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge {{ $language->is_active ? 'bg-soft-success text-success' : 'bg-soft-danger text-danger' }}">
                                                            {{ $language->is_active ? __('active') : __('inactive') }}
                                                        </span>
                                                    </td>
                                                    <td class="text-center">
                                                        @if($language->code === config('app.locale'))
                                                            <span class="badge bg-soft-primary text-primary">
                                                                <i class="feather-star me-1"></i>{{ __('default') }}
                                                            </span>
                                                        @else
                                                            <button class="btn btn-sm btn-outline-primary" onclick="setDefaultLanguage('{{ $language->code }}')">{{ __('set_default') }}</button>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="hstack gap-2 justify-content-center">
                                                            <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="show({{ $language->id }})">
                                                                <i class="feather-eye"></i>
                                                            </a>
                                                            <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="edit({{ $language->id }})">
                                                                <i class="feather-edit-3"></i>
                                                            </a>
                                                            <a href="javascript:void(0);" class="avatar-text avatar-md" wire:click="confirmDelete({{ $language->id }})">
                                                                <i class="feather-trash-2"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="7" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="feather-globe fs-1 mb-3"></i>
                                                            <p>{{ __('no_data') }}</p>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            @if ($languages->hasPages())
                                <div class="card-footer">
                                    {{ $languages->links() }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            @if ($showCreateModal)
                @include('pages.languages.create')
            @endif

            @if ($showEditModal)
                @include('pages.languages.update')
            @endif

            @if ($showViewModal)
                @include('pages.languages.show')
            @endif

            @if ($showDeleteModal)
                @include('pages.languages.delete')
            @endif
        </div>
    </main>
</div>

<script>
function setDefaultLanguage(code) {
    fetch(`/language/set-default/${code}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => console.error('Error:', error));
}
</script>
